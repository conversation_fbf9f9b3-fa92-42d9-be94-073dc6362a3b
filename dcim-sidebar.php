<?php
/**
 * DCIM Sidebar Functions - Navigation Menu Generation
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * Generate consistent sidebar for all pages
 */
function dcim_generate_sidebar($modulelink, $selected_location_id = null) {
    // Output the CSS first (only once per page)
    static $css_output = false;
    if (!$css_output) {
        dcim_generate_sidebar_css();
        $css_output = true;
    }
    
    // Get all data with error handling
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
        $total_servers = Capsule::table('dcim_servers')->count();
        $total_switches = Capsule::table('dcim_switches')->count();
        $total_chassies = Capsule::table('dcim_chassies')->count();
        $total_racks = Capsule::table('dcim_racks')->count();
        $total_subnets = Capsule::table('dcim_subnets')->where('status', 'active')->count();
        $total_ips = Capsule::table('dcim_ip_addresses')->count();
        $assigned_ips = Capsule::table('dcim_ip_addresses')->where('status', 'assigned')->count();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching sidebar data - " . $e->getMessage());
        $locations = collect([]);
        $total_servers = $total_switches = $total_chassies = $total_racks = 0;
        $total_subnets = $total_ips = $assigned_ips = 0;
    }
    
    echo '<div class="dcim-sidebar">';
    
    // Main Menu Header
    echo '<div class="sidebar-header">';
    echo '<div class="sidebar-title">';
    echo '<i class="fas fa-database"></i>DCIM Dashboard';
    echo '</div>';
    echo '<div class="last-updated">';
    echo '<span>Last updated: ' . date('H:i') . '</span>';
    echo '<i class="fas fa-sync-alt"></i>';
    echo '</div>';
    echo '</div>';
    
    // IPAM Section
    echo '<div class="sidebar-section menu-section">';
    echo '<div class="menu-header" onclick="toggleMenuSection(\'ipam\')">';
    echo '<div class="menu-title">';
    echo '<i class="fas fa-network-wired" style="color: #059669;"></i>';
    echo '<span>IPAM</span>';
    echo '<div class="menu-stats">';
    if ($total_ips > 0) {
        $utilization = round(($assigned_ips / $total_ips) * 100, 1);
        $color_class = $utilization > 80 ? 'high-utilization' : ($utilization > 60 ? 'medium-utilization' : 'low-utilization');
        echo '<span class="' . $color_class . '">' . $utilization . '%</span>';
    }
    echo '<span class="stat-badge">' . $total_subnets . '</span>';
    echo '</div>';
    echo '</div>';
    echo '<i class="fas fa-chevron-down toggle-icon"></i>';
    echo '</div>';
    
    echo '<div class="menu-content" id="ipam-menu">';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'dashboard\')">';
    echo '<i class="fas fa-chart-pie"></i>';
    echo '<span>Dashboard</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'subnets\')">';
    echo '<i class="fas fa-sitemap"></i>';
    echo '<span>Subnets</span>';
    echo '<span class="item-count">' . $total_subnets . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'ips\')">';
    echo '<i class="fas fa-list-ol"></i>';
    echo '<span>IP Addresses</span>';
    echo '<span class="item-count">' . $assigned_ips . '/' . $total_ips . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'allocation\')">';
    echo '<i class="fas fa-share-alt"></i>';
    echo '<span>IP Allocation</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Inventory Section
    echo '<div class="sidebar-section menu-section">';
    echo '<div class="menu-header" onclick="toggleMenuSection(\'inventory\')">';
    echo '<div class="menu-title">';
    echo '<i class="fas fa-server" style="color: #dc2626;"></i>';
    echo '<span>Inventory</span>';
    echo '<div class="menu-stats">';
    $total_devices = $total_servers + $total_switches + $total_chassies;
    echo '<span class="stat-badge">' . $total_devices . '</span>';
    echo '</div>';
    echo '</div>';
    echo '<i class="fas fa-chevron-down toggle-icon"></i>';
    echo '</div>';
    
    echo '<div class="menu-content" id="inventory-menu">';
    echo '<div class="menu-item" onclick="navigateTo(\'servers_table\')">';
    echo '<i class="fas fa-server"></i>';
    echo '<span>Servers</span>';
    echo '<span class="item-count">' . $total_servers . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateTo(\'switches_table\')">';
    echo '<i class="fas fa-network-wired"></i>';
    echo '<span>Switches</span>';
    echo '<span class="item-count">' . $total_switches . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateTo(\'chassies\')">';
    echo '<i class="fas fa-hdd"></i>';
    echo '<span>Chassis</span>';
    echo '<span class="item-count">' . $total_chassies . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateTo(\'racks\')">';
    echo '<i class="fas fa-th-large"></i>';
    echo '<span>Racks</span>';
    echo '<span class="item-count">' . $total_racks . '</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Locations Section
    echo '<div class="sidebar-section menu-section">';
    echo '<div class="menu-header" onclick="toggleMenuSection(\'locations\')">';
    echo '<div class="menu-title">';
    echo '<i class="fas fa-map-marker-alt" style="color: #7c3aed;"></i>';
    echo '<span>Locations</span>';
    echo '<div class="menu-stats">';
    echo '<span class="stat-badge">' . count($locations) . '</span>';
    echo '</div>';
    echo '</div>';
    echo '<i class="fas fa-chevron-down toggle-icon"></i>';
    echo '</div>';
    
    echo '<div class="menu-content expanded" id="locations-menu">';
    echo '<div class="menu-item" onclick="navigateTo(\'locations\')">';
    echo '<i class="fas fa-cog"></i>';
    echo '<span>Manage Locations</span>';
    echo '</div>';
    
    echo '<div class="search-container" style="margin: 8px 0;">';
    echo '<div class="search-box">';
    echo '<i class="fas fa-search search-icon"></i>';
    echo '<input type="text" class="search-input" placeholder="Search locations..." id="locationSearch">';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="locations-list" style="max-height: 300px; overflow-y: auto;">';
    if (count($locations) > 0) {
        foreach ($locations as $location) {
            $isActive = ($selected_location_id == $location->id) ? 'active' : '';
            $rack_count = 0;
            
            try {
                $rack_count = Capsule::table('dcim_racks')->where('location_id', $location->id)->count();
            } catch (Exception $e) {
                error_log("DCIM: Error counting racks - " . $e->getMessage());
            }
            
            echo '<div class="location-item ' . $isActive . '" onclick="selectLocation(' . $location->id . ')">';
            echo '<div class="location-flag">' . dcim_get_country_flag($location->country) . '</div>';
            echo '<div class="location-info">';
            echo '<div class="location-name">' . htmlspecialchars($location->name) . '</div>';
            if ($location->city || $location->country) {
                $location_details = [];
                if ($location->city) $location_details[] = $location->city;
                if ($location->country) $location_details[] = $location->country;
                echo '<div style="font-size: 11px; color: #9ca3af; margin-top: 2px;">' . htmlspecialchars(implode(', ', $location_details)) . '</div>';
            }
            if ($rack_count > 0) {
                echo '<div style="font-size: 11px; color: #718096; margin-top: 2px;">' . $rack_count . ' rack' . ($rack_count != 1 ? 's' : '') . '</div>';
            }
            echo '</div>';
            echo '<div class="location-actions">';
            echo '<button class="action-btn" onclick="event.stopPropagation(); editLocation(' . $location->id . ')" title="Edit"><i class="fas fa-edit"></i></button>';
            echo '<button class="action-btn" onclick="event.stopPropagation(); deleteLocation(' . $location->id . ')" title="Delete"><i class="fas fa-trash"></i></button>';
            echo '<button class="action-btn" onclick="event.stopPropagation(); addRack(' . $location->id . ')" title="Add Rack"><i class="fas fa-plus"></i></button>';
            echo '</div>';
            echo '</div>';
        }
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-map-marker-alt"></i>';
        echo '<p>No locations found</p>';
        echo '<p style="font-size: 11px; margin-top: 4px;">Add your first data center location</p>';
        echo '</div>';
    }
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
}

/**
 * Generate shared JavaScript for sidebar interactions
 */
function dcim_generate_sidebar_javascript($modulelink) {
    echo '<script>
    // Hide WHMCS module title on page load
    document.addEventListener("DOMContentLoaded", function() {
        // Hide any H1 elements that contain "DCIM"
        const h1Elements = document.querySelectorAll("h1");
        h1Elements.forEach(function(h1) {
            if (h1.textContent.includes("DCIM")) {
                h1.style.display = "none";
            }
        });
        
        // Also hide common WHMCS title containers
        const titleSelectors = [
            ".contentarea h1",
            ".page-header h1", 
            ".page-title h1",
            ".module-header h1"
        ];
        
        titleSelectors.forEach(function(selector) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(function(element) {
                element.style.display = "none";
            });
        });
    });
    
    function selectLocation(locationId) {
        window.location.href = "' . $modulelink . '&location_id=" + locationId;
    }
    
    function editLocation(locationId) {
        window.location.href = "' . $modulelink . '&action=locations&edit=" + locationId;
    }
    
    function deleteLocation(locationId) {
        if (confirm("Are you sure you want to delete this location and all its racks and servers?")) {
            window.location.href = "' . $modulelink . '&action=locations&delete=" + locationId;
        }
    }
    
    function addRack(locationId) {
        window.location.href = "' . $modulelink . '&action=racks&location_id=" + locationId;
    }
    
    function showAddLocationModal() {
        window.location.href = "' . $modulelink . '&action=locations";
    }
    
    function showAddSubnetModal() {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets";
    }
    
    function navigateToIPAM(subaction) {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=" + subaction;
    }
    
    function navigateTo(action) {
        window.location.href = "' . $modulelink . '&action=" + action;
    }
    
    function toggleMenuSection(sectionId) {
        var menuContent = document.getElementById(sectionId + "-menu");
        var menuHeaders = document.querySelectorAll(".menu-header");
        var toggleIcon = null;
        
        for (var i = 0; i < menuHeaders.length; i++) {
            if (menuHeaders[i].getAttribute("onclick") && menuHeaders[i].getAttribute("onclick").includes(sectionId)) {
                toggleIcon = menuHeaders[i].querySelector(".toggle-icon");
                break;
            }
        }
        
        if (menuContent && toggleIcon) {
            if (menuContent.classList.contains("expanded")) {
                menuContent.classList.remove("expanded");
                toggleIcon.style.transform = "rotate(-90deg)";
            } else {
                menuContent.classList.add("expanded");
                toggleIcon.style.transform = "rotate(0deg)";
            }
        }
    }
    
    // Initialize menu states when DOM is ready
    document.addEventListener("DOMContentLoaded", function() {
        // IPAM and Inventory sections start collapsed, Locations starts expanded
        setTimeout(function() {
            var ipamMenu = document.getElementById("ipam-menu");
            var inventoryMenu = document.getElementById("inventory-menu");
            var locationsMenu = document.getElementById("locations-menu");
            
            // Find toggle icons by their parent menu headers
            var menuHeaders = document.querySelectorAll(".menu-header");
            var ipamIcon = null;
            var inventoryIcon = null;
            var locationsIcon = null;
            
            for (var i = 0; i < menuHeaders.length; i++) {
                var onclick = menuHeaders[i].getAttribute("onclick");
                if (onclick) {
                    if (onclick.includes("ipam")) {
                        ipamIcon = menuHeaders[i].querySelector(".toggle-icon");
                    } else if (onclick.includes("inventory")) {
                        inventoryIcon = menuHeaders[i].querySelector(".toggle-icon");
                    } else if (onclick.includes("locations")) {
                        locationsIcon = menuHeaders[i].querySelector(".toggle-icon");
                    }
                }
            }
            
            if (ipamMenu) {
                ipamMenu.classList.remove("expanded");
                if (ipamIcon) ipamIcon.style.transform = "rotate(-90deg)";
            }
            
            if (inventoryMenu) {
                inventoryMenu.classList.remove("expanded");
                if (inventoryIcon) inventoryIcon.style.transform = "rotate(-90deg)";
            }
            
            if (locationsMenu) {
                locationsMenu.classList.add("expanded");
                if (locationsIcon) locationsIcon.style.transform = "rotate(0deg)";
            }
        }, 100);
    });
    
    // Search functionality
    document.addEventListener("DOMContentLoaded", function() {
        var searchInput = document.getElementById("locationSearch");
        if (searchInput) {
            searchInput.addEventListener("input", function() {
                const searchTerm = this.value.toLowerCase();
                const items = document.querySelectorAll(".location-item");
                
                items.forEach(item => {
                    const text = item.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        item.style.display = "";
                    } else {
                        item.style.display = "none";
                    }
                });
            });
        }
    });
    
    </script>';
}

/**
 * Generate modern CSS styles for sidebar
 */
function dcim_generate_sidebar_css() {
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    echo '<style>
    /* Font Awesome fallbacks using Unicode symbols */
    .fas.fa-database::before { content: "🗄"; }
    .fas.fa-sync-alt::before { content: "🔄"; }
    .fas.fa-network-wired::before { content: "🌐"; }
    .fas.fa-chevron-down::before { content: "▼"; }
    .fas.fa-chart-pie::before { content: "📊"; }
    .fas.fa-sitemap::before { content: "🗂"; }
    .fas.fa-list-ol::before { content: "📋"; }
    .fas.fa-share-alt::before { content: "🔗"; }
    .fas.fa-server::before { content: "🖥"; }
    .fas.fa-hdd::before { content: "💾"; }
    .fas.fa-th-large::before { content: "⊞"; }
    .fas.fa-map-marker-alt::before { content: "📍"; }
    .fas.fa-cog::before { content: "⚙"; }
    .fas.fa-search::before { content: "🔍"; }
    .fas.fa-edit::before { content: "✏"; }
    .fas.fa-trash::before { content: "🗑"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-eye::before { content: "👁"; }
    
    /* DCIM Modern Interface - Embedded CSS */
    
    /* Reset and Base */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    /* Override WHMCS default styles */
    .dcim-container {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
        background: #f8fafc !important;
        margin: 0 !important;
        padding: 0 !important;
        min-height: 100vh !important;
        width: 100% !important;
        color: #1a202c !important;
        line-height: 1.5 !important;
    }

    .dcim-container * {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
    }

    /* Main Layout */
    .dcim-layout {
        display: flex !important;
        min-height: 100vh !important;
        background: #ffffff !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    }

    /* Sidebar */
    .dcim-sidebar {
        width: 300px !important;
        background: #ffffff !important;
        border-right: 1px solid #e2e8f0 !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05) !important;
    }

    /* Sidebar Header */
    .sidebar-header {
        padding: 20px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
    }

    .sidebar-title {
        font-size: 18px !important;
        font-weight: 700 !important;
        color: #ffffff !important;
        margin: 0 0 8px 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }

    .sidebar-title i {
        font-size: 20px !important;
        color: #ffffff !important;
    }

    .last-updated {
        font-size: 12px !important;
        color: rgba(255, 255, 255, 0.8) !important;
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        font-weight: 400 !important;
    }

    .last-updated i {
        font-size: 11px !important;
        color: rgba(255, 255, 255, 0.7) !important;
    }



    /* WHMCS Sidebar Hide/Show */
    .whmcs-sidebar-hidden #sidebar {
        display: none !important;
    }

    .whmcs-sidebar-hidden .contentarea {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .whmcs-sidebar-hidden .dcim-sidebar {
        width: 300px !important;
    }

    /* Hide WHMCS sidebar by default for DCIM module */
    body #sidebar {
        display: none !important;
    }

    body .contentarea {
        margin-left: 0 !important;
        width: 100% !important;
    }

    /* Hide WHMCS module title */
    .contentarea h1,
    .contentarea .page-header h1,
    .contentarea .page-title h1 {
        display: none !important;
    }

    /* Add Location Button */
    .add-location-btn {
        background: #4299e1 !important;
        color: white !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 8px 12px !important;
        font-size: 13px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        transition: all 0.2s ease !important;
        margin-top: 12px !important;
        width: 100% !important;
        justify-content: center !important;
        text-decoration: none !important;
    }

    .add-location-btn:hover {
        background: #3182ce !important;
        transform: translateY(-1px) !important;
    }

    .add-location-btn i {
        font-size: 12px !important;
    }

    /* Search Container */
    .search-container {
        padding: 16px 20px !important;
        border-bottom: 1px solid #e2e8f0 !important;
    }

    .search-box {
        position: relative !important;
        display: flex !important;
        align-items: center !important;
    }

    .search-input {
        width: 100% !important;
        padding: 8px 12px 8px 32px !important;
        border: 1px solid #cbd5e0 !important;
        border-radius: 6px !important;
        font-size: 13px !important;
        background: #f7fafc !important;
        transition: all 0.2s ease !important;
        color: #2d3748 !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #4299e1 !important;
        background: #ffffff !important;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1) !important;
    }

    .search-input::placeholder {
        color: #a0aec0 !important;
    }

    .search-icon {
        position: absolute !important;
        left: 10px !important;
        color: #a0aec0 !important;
        font-size: 13px !important;
        z-index: 2 !important;
    }

    .add-search-btn {
        position: absolute !important;
        right: 6px !important;
        background: #4299e1 !important;
        color: white !important;
        border: none !important;
        border-radius: 4px !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        font-size: 11px !important;
    }

    .add-search-btn:hover {
        background: #3182ce !important;
    }

    /* Locations List */
    .locations-list {
        flex: 1 !important;
        overflow-y: auto !important;
        padding: 8px 0 !important;
        max-height: 400px !important;
    }

    .location-item {
        padding: 14px 20px !important;
        cursor: pointer !important;
        border-bottom: 1px solid #f1f5f9 !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        position: relative !important;
        margin: 0 8px !important;
        border-radius: 8px !important;
        margin-bottom: 4px !important;
    }

    .location-item:hover {
        background: #f7fafc !important;
        transform: translateX(4px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .location-item.active {
        background: linear-gradient(135deg, #ebf8ff 0%, #e6fffa 100%) !important;
        border-right: 4px solid #4299e1 !important;
        box-shadow: 0 2px 12px rgba(66, 153, 225, 0.15) !important;
    }

    .location-flag {
        width: 20px !important;
        height: 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        flex-shrink: 0 !important;
    }

    .location-info {
        flex: 1 !important;
        min-width: 0 !important;
    }

    .location-name {
        font-weight: 500 !important;
        color: #2d3748 !important;
        font-size: 14px !important;
        margin: 0 !important;
        line-height: 1.3 !important;
    }

    .location-actions {
        display: flex !important;
        gap: 4px !important;
        opacity: 0 !important;
        transition: opacity 0.2s ease !important;
    }

    .location-item:hover .location-actions {
        opacity: 1 !important;
    }

    .action-btn {
        background: none !important;
        border: none !important;
        color: #6b7280 !important;
        cursor: pointer !important;
        padding: 8px !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
        font-size: 12px !important;
        min-width: auto !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 32px !important;
        height: 32px !important;
        margin: 0 2px !important;
        position: relative !important;
    }

    .action-btn:hover {
        background: #f3f4f6 !important;
        color: #374151 !important;
        transform: scale(1.1) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .action-btn:active {
        transform: scale(0.95) !important;
    }

    .action-btn i {
        font-size: 13px !important;
        line-height: 1 !important;
        color: inherit !important;
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        display: block !important;
        width: 100% !important;
        text-align: center !important;
    }

    .action-btn i.fa-eye {
        color: #3b82f6 !important;
    }

    .action-btn i.fa-edit {
        color: #10b981 !important;
    }

    .action-btn i.fa-trash {
        color: #ef4444 !important;
    }

    .action-btn i.fa-plus {
        color: #8b5cf6 !important;
    }

    .action-btn i.fa-server,
    .add-location-btn i.fa-server {
        color: inherit !important;
    }

    /* New Menu Section Styles */
    .menu-section {
        border-bottom: 1px solid #e5e7eb !important;
        margin-bottom: 0 !important;
    }

    .menu-header {
        padding: 16px 20px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        transition: all 0.2s ease !important;
        background: #ffffff !important;
        border: none !important;
        width: 100% !important;
        text-align: left !important;
        position: relative !important;
    }

    .menu-header:hover {
        background: #f8fafc !important;
    }

    .menu-header:active {
        background: #f1f5f9 !important;
    }

    .menu-title {
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        flex: 1 !important;
    }

    .menu-title i {
        font-size: 16px !important;
        width: 20px !important;
        text-align: center !important;
        flex-shrink: 0 !important;
    }

    .menu-title span {
        font-size: 15px !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
    }

    .menu-stats {
        display: flex !important;
        gap: 6px !important;
        margin-left: 8px !important;
    }

    .menu-stats span {
        font-size: 10px !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 10px !important;
        font-weight: 500 !important;
        min-width: 20px !important;
        text-align: center !important;
        line-height: 1.2 !important;
    }

    .stat-badge {
        background: #6b7280 !important;
    }

    .high-utilization {
        background: #ef4444 !important;
    }

    .medium-utilization {
        background: #f59e0b !important;
    }

    .low-utilization {
        background: #10b981 !important;
    }

    .toggle-icon {
        font-size: 12px !important;
        color: #6b7280 !important;
        transition: transform 0.3s ease !important;
        transform: rotate(0deg) !important;
        flex-shrink: 0 !important;
    }

    .menu-section.expanded .toggle-icon {
        transform: rotate(180deg) !important;
    }

    .menu-content {
        max-height: 0 !important;
        overflow: hidden !important;
        transition: max-height 0.3s ease !important;
        background: #fafbfc !important;
    }

    .menu-content.expanded {
        max-height: 800px !important;
    }

    .menu-item {
        padding: 12px 20px 12px 52px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        border: none !important;
        background: none !important;
        color: #374151 !important;
        font-size: 14px !important;
        text-decoration: none !important;
        border-bottom: 1px solid #f1f5f9 !important;
        position: relative !important;
    }

    .menu-item:hover {
        background: #f1f5f9 !important;
        color: #1f2937 !important;
        transform: translateX(4px) !important;
    }

    .menu-item:active {
        background: #e2e8f0 !important;
        transform: translateX(2px) !important;
    }

    .menu-item i {
        width: 16px !important;
        flex-shrink: 0 !important;
        text-align: center !important;
        color: #6b7280 !important;
        font-size: 13px !important;
    }

    .menu-item:hover i {
        color: #374151 !important;
    }

    .menu-item span:first-of-type {
        flex: 1 !important;
        font-weight: 500 !important;
    }

    .item-count {
        font-size: 11px !important;
        color: #6b7280 !important;
        background: #f3f4f6 !important;
        padding: 3px 8px !important;
        border-radius: 12px !important;
        font-weight: 500 !important;
        min-width: 24px !important;
        text-align: center !important;
        line-height: 1.2 !important;
    }

    .menu-item:hover .item-count {
        background: #ecfdf5 !important;
        color: #059669 !important;
    }

    /* Force FontAwesome icons to be visible */
    .action-btn i::before {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-transform: none !important;
        line-height: 1 !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
    }

    /* Ensure icons dont get overridden */
    .dcim-container .action-btn i {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Empty State */
    .empty-state {
        text-align: center !important;
        padding: 60px 20px !important;
        color: #718096 !important;
    }

    .empty-state i {
        font-size: 48px !important;
        margin-bottom: 16px !important;
        opacity: 0.3 !important;
    }

    .empty-state h3 {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #4a5568 !important;
        margin: 0 0 8px 0 !important;
    }

    .empty-state p {
        font-size: 14px !important;
        color: #718096 !important;
        margin: 0 !important;
    }

    /* Main Content Area */
    .dcim-main {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        background: #f8fafc !important;
    }

    /* Main Header */
    .main-header {
        padding: 20px 24px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        background: #ffffff !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
    }

    .back-btn {
        background: none !important;
        border: none !important;
        color: #718096 !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .back-btn:hover {
        background: #f1f5f9 !important;
        color: #4a5568 !important;
    }

    .main-title {
        font-size: 20px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dcim-layout {
            flex-direction: column !important;
        }
        
        .dcim-sidebar {
            width: 100% !important;
            max-height: 200px !important;
            border-right: none !important;
            border-bottom: 1px solid #e2e8f0 !important;
        }
        
        .locations-list {
            max-height: 120px !important;
        }
        
        .main-header {
            padding: 16px 20px !important;
        }
    }

    /* Custom Scrollbar */
    .locations-list::-webkit-scrollbar {
        width: 6px !important;
    }

    .locations-list::-webkit-scrollbar-track {
        background: #f1f5f9 !important;
        border-radius: 3px !important;
    }

    .locations-list::-webkit-scrollbar-thumb {
        background: #cbd5e0 !important;
        border-radius: 3px !important;
        transition: background 0.2s ease !important;
    }

    .locations-list::-webkit-scrollbar-thumb:hover {
        background: #a0aec0 !important;
    }

    /* Animations */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .location-item {
        animation: fadeIn 0.3s ease-out !important;
    }

    .menu-item {
        animation: slideIn 0.2s ease-out !important;
    }

    /* Loading States */
    .loading {
        opacity: 0.6 !important;
        pointer-events: none !important;
    }

    .loading::after {
        content: "" !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 20px !important;
        height: 20px !important;
        margin: -10px 0 0 -10px !important;
        border: 2px solid #e2e8f0 !important;
        border-top: 2px solid #4299e1 !important;
        border-radius: 50% !important;
        animation: spin 1s linear infinite !important;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* IPAM Specific Styles */
    .stat-card {
        background: #ffffff !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 12px !important;
        padding: 24px !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
        transition: all 0.2s ease !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }

    .stat-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        transform: translateY(-2px) !important;
    }

    .stat-icon {
        width: 48px !important;
        height: 48px !important;
        border-radius: 12px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 20px !important;
    }

    .stat-content {
        flex: 1 !important;
    }

    .stat-number {
        font-size: 28px !important;
        font-weight: 700 !important;
        color: #1f2937 !important;
        line-height: 1 !important;
        margin-bottom: 4px !important;
    }

    .stat-label {
        font-size: 14px !important;
        color: #6b7280 !important;
        font-weight: 500 !important;
    }

    .table-container {
        background: #ffffff !important;
        border-radius: 12px !important;
        border: 1px solid #e5e7eb !important;
        overflow: hidden !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    }

    .table-header {
        padding: 20px 24px !important;
        border-bottom: 1px solid #e5e7eb !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        background: #f9fafb !important;
    }

    .table-header h3 {
        margin: 0 !important;
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
    }

    .modern-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 14px !important;
    }

    .modern-table th {
        background: #f9fafb !important;
        padding: 12px 16px !important;
        text-align: left !important;
        font-weight: 600 !important;
        color: #374151 !important;
        border-bottom: 1px solid #e5e7eb !important;
        font-size: 12px !important;
        text-transform: uppercase !important;
        letter-spacing: 0.05em !important;
    }

    .modern-table td {
        padding: 16px !important;
        border-bottom: 1px solid #f3f4f6 !important;
        color: #1f2937 !important;
        vertical-align: top !important;
    }

    .modern-table tr:hover {
        background: #f9fafb !important;
    }

    .modern-table tr:last-child td {
        border-bottom: none !important;
    }

    /* Modal Styles */
    .modal {
        position: fixed !important;
        z-index: 9999 !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        pointer-events: auto !important;
    }

    .modal-content {
        background: #ffffff !important;
        border-radius: 12px !important;
        width: 90% !important;
        max-width: 500px !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    }

    .modal-header {
        padding: 24px 24px 0 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        border-bottom: 1px solid #e5e7eb !important;
        margin-bottom: 24px !important;
        padding-bottom: 16px !important;
    }

    .modal-header h2 {
        margin: 0 !important;
        font-size: 20px !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
    }

    .close {
        color: #6b7280 !important;
        font-size: 24px !important;
        font-weight: bold !important;
        cursor: pointer !important;
        border: none !important;
        background: none !important;
        padding: 4px !important;
        border-radius: 4px !important;
        transition: all 0.2s ease !important;
    }

    .close:hover {
        color: #374151 !important;
        background: #f3f4f6 !important;
    }

    .form-group {
        margin-bottom: 20px !important;
        padding: 0 24px !important;
    }

    .form-group label {
        display: block !important;
        margin-bottom: 6px !important;
        font-weight: 500 !important;
        color: #374151 !important;
        font-size: 14px !important;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-family: inherit !important;
        font-size: 14px !important;
        transition: all 0.2s ease !important;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none !important;
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    .modal-footer {
        padding: 24px !important;
        border-top: 1px solid #e5e7eb !important;
        display: flex !important;
        gap: 12px !important;
        justify-content: flex-end !important;
        margin-top: 24px !important;
    }

    .btn {
        padding: 10px 20px !important;
        border-radius: 6px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        border: 1px solid transparent !important;
        transition: all 0.2s ease !important;
        text-decoration: none !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .btn-primary {
        background: #3b82f6 !important;
        color: white !important;
        border-color: #3b82f6 !important;
    }

    .btn-primary:hover {
        background: #2563eb !important;
        border-color: #2563eb !important;
    }

    .btn-secondary {
        background: #ffffff !important;
        color: #374151 !important;
        border-color: #d1d5db !important;
    }

    .btn-secondary:hover {
        background: #f9fafb !important;
        border-color: #9ca3af !important;
    }

    .alert {
        padding: 12px 16px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
    }

    .alert-success {
        background: #ecfdf5 !important;
        color: #065f46 !important;
        border: 1px solid #a7f3d0 !important;
    }

    .alert-danger {
        background: #fef2f2 !important;
        color: #991b1b !important;
        border: 1px solid #fecaca !important;
    }

    /* Ensure clickable elements work */
    button, .btn, .action-btn, .menu-item, .location-item {
        pointer-events: auto !important;
        cursor: pointer !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* Fix any overlay issues */
    .dcim-container * {
        pointer-events: auto !important;
    }

    /* Ensure modal content is clickable */
    .modal-content {
        pointer-events: auto !important;
        position: relative !important;
        z-index: 10000 !important;
    }
    </style>';
}

?> 