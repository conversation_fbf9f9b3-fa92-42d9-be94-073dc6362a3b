<?php
/**
 * <PERSON>IM IPAM (IP Address Management) Functions
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * Main IPAM management router
 */
function dcim_manage_ipam($modulelink) {
    $subaction = $_GET['subaction'] ?? 'dashboard';
    
    switch ($subaction) {
        case 'subnets':
            dcim_manage_subnets($modulelink);
            break;
        case 'ips':
            dcim_manage_ip_addresses($modulelink);
            break;
        case 'allocation':
            dcim_manage_ip_allocation($modulelink);
            break;
        case 'dashboard':
        default:
            dcim_ipam_dashboard($modulelink);
            break;
    }
}

/**
 * IPAM Dashboard
 */
function dcim_ipam_dashboard($modulelink) {
    // Get statistics
    try {
        $total_subnets = Capsule::table('dcim_subnets')->where('status', 'active')->count();
        $total_ips = Capsule::table('dcim_ip_addresses')->count();
        $assigned_ips = Capsule::table('dcim_ip_addresses')->where('status', 'assigned')->count();
        $available_ips = Capsule::table('dcim_ip_addresses')->where('status', 'available')->count();
        $reserved_ips = Capsule::table('dcim_ip_addresses')->where('status', 'reserved')->count();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching IPAM stats - " . $e->getMessage());
        $total_subnets = $total_ips = $assigned_ips = $available_ips = $reserved_ips = 0;
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">';
    echo '<i class="fas fa-network-wired" style="margin-right: 12px; color: #059669;"></i>';
    echo 'IPAM Dashboard';
    echo '</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showAddSubnetModal()">';
    echo '<i class="fas fa-plus"></i> Add Subnet';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Statistics cards
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 32px;">';
    
    // Total Subnets
    echo '<div class="stat-card">';
    echo '<div class="stat-icon" style="background: #059669;"><i class="fas fa-sitemap"></i></div>';
    echo '<div class="stat-content">';
    echo '<div class="stat-number">' . $total_subnets . '</div>';
    echo '<div class="stat-label">Active Subnets</div>';
    echo '</div>';
    echo '</div>';
    
    // Total IPs
    echo '<div class="stat-card">';
    echo '<div class="stat-icon" style="background: #3b82f6;"><i class="fas fa-list-ol"></i></div>';
    echo '<div class="stat-content">';
    echo '<div class="stat-number">' . $total_ips . '</div>';
    echo '<div class="stat-label">Total IP Addresses</div>';
    echo '</div>';
    echo '</div>';
    
    // Assigned IPs
    echo '<div class="stat-card">';
    echo '<div class="stat-icon" style="background: #f59e0b;"><i class="fas fa-check-circle"></i></div>';
    echo '<div class="stat-content">';
    echo '<div class="stat-number">' . $assigned_ips . '</div>';
    echo '<div class="stat-label">Assigned IPs</div>';
    echo '</div>';
    echo '</div>';
    
    // Available IPs
    echo '<div class="stat-card">';
    echo '<div class="stat-icon" style="background: #10b981;"><i class="fas fa-circle"></i></div>';
    echo '<div class="stat-content">';
    echo '<div class="stat-number">' . $available_ips . '</div>';
    echo '<div class="stat-label">Available IPs</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    
    // Recent subnets table
    echo '<div class="table-container">';
    echo '<div class="table-header">';
    echo '<h3>Recent Subnets</h3>';
    echo '<a href="' . $modulelink . '&action=ipam&subaction=subnets" class="btn btn-primary">View All</a>';
    echo '</div>';
    
    try {
        $recent_subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->where('dcim_subnets.status', 'active')
            ->orderBy('dcim_subnets.created_at', 'desc')
            ->limit(5)
            ->get();
            
        if (count($recent_subnets) > 0) {
            echo '<table class="modern-table">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>Subnet</th>';
            echo '<th>Location</th>';
            echo '<th>Gateway</th>';
            echo '<th>Status</th>';
            echo '<th>Created</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($recent_subnets as $subnet) {
                echo '<tr>';
                echo '<td><strong>' . htmlspecialchars($subnet->network . '/' . $subnet->prefix_length) . '</strong><br>';
                echo '<small>' . htmlspecialchars($subnet->name) . '</small></td>';
                echo '<td>' . htmlspecialchars($subnet->location_name ?: 'Unassigned') . '</td>';
                echo '<td>' . htmlspecialchars($subnet->gateway ?: '-') . '</td>';
                echo '<td><span class="status-badge status-' . $subnet->status . '">' . ucfirst($subnet->status) . '</span></td>';
                echo '<td>' . date('M j, Y', strtotime($subnet->created_at)) . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        } else {
            echo '<div class="empty-state">';
            echo '<i class="fas fa-sitemap"></i>';
            echo '<h3>No Subnets Found</h3>';
            echo '<p>Create your first subnet to get started with IP address management.</p>';
            echo '<button class="add-location-btn" onclick="showAddSubnetModal()" style="margin-top: 16px;">';
            echo '<i class="fas fa-plus"></i> Add First Subnet';
            echo '</button>';
            echo '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error loading subnets: ' . $e->getMessage() . '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Include shared JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Manage Subnets Interface
 */
function dcim_manage_subnets($modulelink) {
    // Handle form submissions
    if ($_POST['action'] == 'add_subnet') {
        $result = dcim_create_subnet(
            $_POST['name'],
            $_POST['network_cidr'], // Use the CIDR input field
            null, // prefix_length will be parsed from CIDR
            $_POST['location_id'] ?: null,
            $_POST['gateway'] ?: null,
            $_POST['dns_primary'] ?: null,
            $_POST['dns_secondary'] ?: null,
            $_POST['vlan_id'] ?: null,
            $_POST['description'] ?: null
        );
        
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet created successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error creating subnet: ' . $result['error'] . '</div>';
        }
    }
    
    if ($_POST['action'] == 'delete_subnet') {
        $result = dcim_delete_subnet($_POST['subnet_id']);
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet deleted successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error deleting subnet: ' . $result['error'] . '</div>';
        }
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">';
    echo '<i class="fas fa-sitemap" style="margin-right: 12px; color: #059669;"></i>';
    echo 'Subnets Management';
    echo '</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<span style="font-size: 12px; color: #6b7280; margin-right: 8px;">Last updated: ' . date('H:i') . '</span>';
    echo '<i class="fas fa-sync-alt" style="color: #6b7280; cursor: pointer;" onclick="location.reload()"></i>';
    echo '<button class="add-location-btn" onclick="showAddSubnetModal()" style="margin-left: 12px;">';
    echo '<i class="fas fa-plus"></i> Add New Subnet';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    // Continue with subnet management interface...
    dcim_render_subnets_table($modulelink);
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Include shared JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    
    // Add subnet modal and JavaScript
    dcim_render_add_subnet_modal($modulelink);
}

/**
 * Render subnets table
 */
function dcim_render_subnets_table($modulelink) {
    // Get filters
    $status_filter = $_GET['status'] ?? 'all';
    $location_filter = $_GET['location'] ?? 'all';
    $search = $_GET['search'] ?? '';

    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';

    // Filters
    echo '<div style="display: flex; gap: 16px; margin-bottom: 24px; align-items: center;">';

    // Status filter
    echo '<select onchange="updateFilter(\'status\', this.value)" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '<option value="all"' . ($status_filter == 'all' ? ' selected' : '') . '>All Status</option>';
    echo '<option value="active"' . ($status_filter == 'active' ? ' selected' : '') . '>Active</option>';
    echo '<option value="reserved"' . ($status_filter == 'reserved' ? ' selected' : '') . '>Reserved</option>';
    echo '<option value="deprecated"' . ($status_filter == 'deprecated' ? ' selected' : '') . '>Deprecated</option>';
    echo '</select>';

    // Location filter
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
        echo '<select onchange="updateFilter(\'location\', this.value)" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
        echo '<option value="all"' . ($location_filter == 'all' ? ' selected' : '') . '>All Locations</option>';
        foreach ($locations as $location) {
            $selected = ($location_filter == $location->id) ? ' selected' : '';
            echo '<option value="' . $location->id . '"' . $selected . '>' . htmlspecialchars($location->name) . '</option>';
        }
        echo '</select>';
    } catch (Exception $e) {
        echo '<span style="color: #ef4444;">Error loading locations</span>';
    }

    // Search
    echo '<div style="flex: 1; max-width: 300px;">';
    echo '<input type="text" placeholder="Search subnets..." value="' . htmlspecialchars($search) . '" ';
    echo 'onkeyup="searchSubnets(this.value)" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '</div>';

    echo '</div>';

    // Subnets table
    echo '<div class="table-container">';
    echo '<div class="table-header">';
    echo '<h3>Subnets Management</h3>';
    echo '<div style="font-size: 12px; color: #6b7280;">Showing ';

    try {
        // Build query
        $query = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name');

        if ($status_filter != 'all') {
            $query->where('dcim_subnets.status', $status_filter);
        }

        if ($location_filter != 'all') {
            $query->where('dcim_subnets.location_id', $location_filter);
        }

        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('dcim_subnets.name', 'like', '%' . $search . '%')
                  ->orWhere('dcim_subnets.network', 'like', '%' . $search . '%')
                  ->orWhere('dcim_locations.name', 'like', '%' . $search . '%');
            });
        }

        $total_count = $query->count();
        $subnets = $query->orderBy('dcim_subnets.created_at', 'desc')->get();

        echo '1 to ' . count($subnets) . ' of ' . $total_count . ' subnets</div>';
        echo '</div>';

        if (count($subnets) > 0) {
            echo '<table class="modern-table">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>SUBNET</th>';
            echo '<th>STATUS</th>';
            echo '<th>LOCATION</th>';
            echo '<th>CATEGORY</th>';
            echo '<th>NOTE</th>';
            echo '<th>ACTIONS</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';

            foreach ($subnets as $subnet) {
                // Get IP utilization
                $total_ips = Capsule::table('dcim_ip_addresses')->where('subnet_id', $subnet->id)->count();
                $assigned_ips = Capsule::table('dcim_ip_addresses')->where('subnet_id', $subnet->id)->where('status', 'assigned')->count();

                echo '<tr onclick="showSubnetDetails(' . $subnet->id . ')" style="cursor: pointer;">';
                echo '<td>';
                echo '<strong>' . htmlspecialchars($subnet->network . '/' . $subnet->prefix_length) . '</strong>';
                if ($total_ips > 0) {
                    $utilization = round(($assigned_ips / $total_ips) * 100, 1);
                    $color = $utilization > 80 ? '#ef4444' : ($utilization > 60 ? '#f59e0b' : '#10b981');
                    echo '<br><small style="color: ' . $color . ';">' . $assigned_ips . '/' . $total_ips . ' IPs (' . $utilization . '%)</small>';
                }
                echo '</td>';

                $status_colors = [
                    'active' => '#10b981',
                    'reserved' => '#f59e0b',
                    'deprecated' => '#6b7280'
                ];
                $status_color = $status_colors[$subnet->status] ?? '#6b7280';
                echo '<td><span style="color: ' . $status_color . '; font-weight: 500;"><i class="fas fa-circle" style="font-size: 8px; margin-right: 6px;"></i>' . ucfirst($subnet->status) . '</span></td>';

                echo '<td>' . htmlspecialchars($subnet->location_name ?: 'Unassigned') . '</td>';
                echo '<td>Root</td>'; // Default category as shown in your image
                echo '<td>' . (empty($subnet->description) ? '-' : htmlspecialchars(substr($subnet->description, 0, 30) . (strlen($subnet->description) > 30 ? '...' : ''))) . '</td>';
                echo '<td>';
                echo '<button class="action-btn" onclick="event.stopPropagation(); showSubnetDetails(' . $subnet->id . ')" title="View Details"><i class="fas fa-eye"></i></button>';
                echo '<button class="action-btn" onclick="event.stopPropagation(); deleteSubnet(' . $subnet->id . ')" title="Delete"><i class="fas fa-trash"></i></button>';
                echo '</td>';
                echo '</tr>';
            }

            echo '</tbody>';
            echo '</table>';
        } else {
            echo '<div class="empty-state">';
            echo '<i class="fas fa-sitemap"></i>';
            echo '<h3>No Subnets Found</h3>';
            echo '<p>No subnets match your current filters.</p>';
            echo '</div>';
        }

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error loading subnets: ' . $e->getMessage() . '</div>';
    }

    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Render Add Subnet Modal
 */
function dcim_render_add_subnet_modal($modulelink) {
    echo '<div id="addSubnetModal" class="modal" style="display: none;">';
    echo '<div class="modal-content">';
    echo '<div class="modal-header">';
    echo '<h2>Add New Subnet</h2>';
    echo '<span class="close" onclick="closeAddSubnetModal()">&times;</span>';
    echo '</div>';
    echo '<form method="post" action="' . $modulelink . '&action=ipam&subaction=subnets">';
    echo '<input type="hidden" name="action" value="add_subnet">';

    echo '<div class="form-group">';
    echo '<label>IP Version *</label>';
    echo '<select name="ip_version" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '<option value="ipv4">IPv4</option>';
    echo '</select>';
    echo '</div>';

    echo '<div class="form-group">';
    echo '<label>Subnet (CIDR notation) *</label>';
    echo '<input type="text" name="network_cidr" placeholder="e.g., ***********/24" required ';
    echo 'style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '</div>';

    echo '<div class="form-group">';
    echo '<label>Country</label>';
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
        echo '<select name="location_id" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
        echo '<option value="">-- Select Country --</option>';
        foreach ($locations as $location) {
            echo '<option value="' . $location->id . '">' . htmlspecialchars($location->name) . '</option>';
        }
        echo '</select>';
    } catch (Exception $e) {
        echo '<input type="text" placeholder="Location not available" disabled style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    }
    echo '</div>';

    echo '<div class="form-group">';
    echo '<label>City *</label>';
    echo '<input type="text" name="name" placeholder="Enter city name" required ';
    echo 'style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '</div>';

    echo '<div class="form-group">';
    echo '<label>Public Subnet</label>';
    echo '<div style="display: flex; gap: 16px; margin-top: 8px;">';
    echo '<label style="display: flex; align-items: center; gap: 8px;"><input type="radio" name="public_subnet" value="yes"> Yes</label>';
    echo '<label style="display: flex; align-items: center; gap: 8px;"><input type="radio" name="public_subnet" value="no" checked> No</label>';
    echo '</div>';
    echo '</div>';

    echo '<div class="form-group">';
    echo '<label>Subnet Type</label>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-top: 8px;">';
    echo '<label style="display: flex; align-items: center; gap: 8px;"><input type="radio" name="subnet_type" value="root" checked> Root</label>';
    echo '<label style="display: flex; align-items: center; gap: 8px;"><input type="radio" name="subnet_type" value="customer"> Customer</label>';
    echo '<label style="display: flex; align-items: center; gap: 8px;"><input type="radio" name="subnet_type" value="management"> Management</label>';
    echo '<label style="display: flex; align-items: center; gap: 8px;"><input type="radio" name="subnet_type" value="transit"> Transit</label>';
    echo '</div>';
    echo '</div>';

    echo '<div class="form-group">';
    echo '<label>Note</label>';
    echo '<textarea name="description" placeholder="Optional note for this subnet..." rows="3" ';
    echo 'style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; resize: vertical;"></textarea>';
    echo '</div>';

    echo '<div class="modal-footer">';
    echo '<button type="button" onclick="closeAddSubnetModal()" class="btn btn-secondary">Cancel</button>';
    echo '<button type="submit" class="btn btn-primary">Add Subnet</button>';
    echo '</div>';

    echo '</form>';
    echo '</div>';
    echo '</div>';

    // Add modal JavaScript
    echo '<script>
    function showAddSubnetModal() {
        document.getElementById("addSubnetModal").style.display = "block";
    }

    function closeAddSubnetModal() {
        document.getElementById("addSubnetModal").style.display = "none";
    }

    function updateFilter(type, value) {
        const url = new URL(window.location);
        url.searchParams.set(type, value);
        window.location.href = url.toString();
    }

    function searchSubnets(value) {
        const url = new URL(window.location);
        if (value) {
            url.searchParams.set("search", value);
        } else {
            url.searchParams.delete("search");
        }
        setTimeout(() => {
            window.location.href = url.toString();
        }, 500);
    }

    function showSubnetDetails(subnetId) {
        // Create a simple details modal
        const modal = document.createElement("div");
        modal.className = "modal";
        modal.style.display = "block";
        modal.innerHTML = \'<div class="modal-content">\' +
            \'<div class="modal-header">\' +
            \'<h2>Subnet Details</h2>\' +
            \'<span class="close" onclick="this.closest(\\\'.modal\\\').remove()">&times;</span>\' +
            \'</div>\' +
            \'<div style="padding: 24px;">\' +
            \'<p>Loading subnet details for ID: \' + subnetId + \'...</p>\' +
            \'<p><em>Detailed subnet view with IP hierarchy will be implemented in the next phase.</em></p>\' +
            \'</div>\' +
            \'<div class="modal-footer">\' +
            \'<button type="button" onclick="this.closest(\\\'.modal\\\').remove()" class="btn btn-secondary">Close</button>\' +
            \'</div>\' +
            \'</div>\';
        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.onclick = function(event) {
            if (event.target === modal) {
                modal.remove();
            }
        }
    }

    function deleteSubnet(subnetId) {
        if (confirm("Are you sure you want to delete this subnet? This will also delete all associated IP addresses.")) {
            const form = document.createElement("form");
            form.method = "POST";
            form.action = window.location.href;

            const actionInput = document.createElement("input");
            actionInput.type = "hidden";
            actionInput.name = "action";
            actionInput.value = "delete_subnet";

            const idInput = document.createElement("input");
            idInput.type = "hidden";
            idInput.name = "subnet_id";
            idInput.value = subnetId;

            form.appendChild(actionInput);
            form.appendChild(idInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        const modal = document.getElementById("addSubnetModal");
        if (event.target == modal) {
            closeAddSubnetModal();
        }
    }
    </script>';
}

/**
 * Placeholder functions for other IPAM interfaces
 */
function dcim_manage_ip_addresses($modulelink) {
    echo '<div class="dcim-container">Coming soon: IP Address Management</div>';
}

function dcim_manage_ip_allocation($modulelink) {
    echo '<div class="dcim-container">Coming soon: IP Allocation Interface</div>';
}

?>
