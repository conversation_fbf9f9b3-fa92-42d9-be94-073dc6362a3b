<?php

/**
 * <PERSON><PERSON> IPAM (IP Address Management) Module
 * 
 * This file contains all functions related to managing IP addresses and subnets
 * in the DCIM system, including subnet creation, IP allocation, and network management.
 * 
 * Dependencies:
 * - dcim-core.php (database tables, IPAM utility functions)
 * - dcim-sidebar.php (navigation interface)
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Ensure this file is only included within WHMCS context
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

// Include required dependencies
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';

/**
 * IPAM Dashboard - Main entry point for IPAM functionality
 */
function dcim_ipam_dashboard($modulelink) {
    $subaction = $_GET['subaction'] ?? 'dashboard';

    // Handle AJAX requests
    if ($subaction === 'subnet_details' && !empty($_GET['subnet_id'])) {
        dcim_handle_subnet_details_ajax($_GET['subnet_id']);
        return;
    }

    switch ($subaction) {
        case 'subnets':
            dcim_manage_subnets($modulelink);
            break;
        case 'ips':
            dcim_manage_ips($modulelink);
            break;
        case 'allocation':
            dcim_ip_allocation($modulelink);
            break;
        default:
            dcim_ipam_overview($modulelink);
            break;
    }
}

/**
 * Handle AJAX request for subnet details
 */
function dcim_handle_subnet_details_ajax($subnet_id) {
    header('Content-Type: application/json');

    try {
        $subnet = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->where('dcim_subnets.id', $subnet_id)
            ->first();

        if (!$subnet) {
            echo json_encode(['success' => false, 'error' => 'Subnet not found']);
            exit;
        }

        echo json_encode(['success' => true, 'subnet' => $subnet]);
        exit;
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}

/**
 * IPAM Overview Dashboard
 */
function dcim_ipam_overview($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    dcim_force_create_ipam_tables();
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">IPAM Dashboard</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    echo '<h2>IP Address Management Overview</h2>';
    echo '<p>Welcome to the IPAM module. Use the sidebar to navigate to different sections.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Subnet Management Interface
 */
function dcim_manage_subnets($modulelink) {
    // Ensure tables exist with error handling
    if (!dcim_ensure_tables_exist()) {
        echo '<div class="alert alert-danger">Error: Could not create database tables. Please check your database permissions.</div>';
        return;
    }

    // Force table creation if they don't exist
    dcim_force_create_ipam_tables();
    
    // Handle form submissions
    if ($_POST['action'] === 'create_subnet') {
        $result = dcim_create_subnet($_POST);
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet created successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($result['error']) . '</div>';
        }
    }
    
    if ($_POST['action'] === 'delete_subnet' && !empty($_POST['subnet_id'])) {
        $result = dcim_delete_subnet($_POST['subnet_id']);
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet deleted successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($result['error']) . '</div>';
        }
    }
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-sitemap::before { content: "⚡"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-search::before { content: "🔍"; }
    .fas.fa-filter::before { content: "⚙"; }
    </style>';
    
    try {
        // Get subnets with location information
        $subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->orderBy('dcim_subnets.subnet')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching subnets: ' . $e->getMessage() . '</div>';
        $subnets = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">Subnets Management</div>';
    echo '<div style="font-size: 12px; color: #6b7280;">Last updated: ' . date('g:i:s A') . '</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="showAddSubnetModal()" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px; background: #6366f1; color: white; padding: 8px 16px; border-radius: 6px;"><i class="fas fa-plus"></i> Add New Subnet</button>';
    echo '</div>';
    echo '</div>';
    
    // Filters and search
    echo '<div style="padding: 0 32px; margin-bottom: 16px;">';
    echo '<div style="display: flex; gap: 16px; align-items: center;">';
    echo '<select style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option>All Status</option>';
    echo '<option>Available</option>';
    echo '<option>Parent of Allocated Subnet</option>';
    echo '</select>';
    echo '<select style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option>All Locations</option>';
    echo '</select>';
    echo '<div style="position: relative; margin-left: auto;">';
    echo '<input type="text" placeholder="Search subnets..." style="padding: 8px 12px 8px 36px; border: 1px solid #d1d5db; border-radius: 6px; width: 250px;">';
    echo '<i class="fas fa-search" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Subnets table
    if (count($subnets) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">SUBNET</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">STATUS</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">LOCATION</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">CATEGORY</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">NOTE</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">ACTIONS</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($subnets as $subnet) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($subnet->subnet) . '</div>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            if ($subnet->status === 'Available') {
                echo '<span style="background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"><i class="fas fa-check-circle" style="margin-right: 4px;"></i>Available</span>';
            } else {
                echo '<span style="background: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"><i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>' . htmlspecialchars($subnet->status) . '</span>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($subnet->city && $subnet->country) {
                echo htmlspecialchars($subnet->city . ', ' . $subnet->country);
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . htmlspecialchars($subnet->subnet_type) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . (htmlspecialchars($subnet->note) ?: '-') . '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewSubnetDetails(' . $subnet->id . ')" style="background: #f3f4f6; border: none; padding: 6px 8px; border-radius: 4px; cursor: pointer;" title="View Details"><i class="fas fa-eye"></i></button>';
            echo '<button onclick="deleteSubnet(' . $subnet->id . ')" style="background: #fef2f2; border: none; padding: 6px 8px; border-radius: 4px; cursor: pointer; color: #dc2626;" title="Delete"><i class="fas fa-trash"></i></button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
        
        // Pagination
        echo '<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px;">';
        echo '<div style="color: #6b7280; font-size: 14px;">Showing 1 to ' . count($subnets) . ' of ' . count($subnets) . ' subnets</div>';
        echo '<div style="display: flex; gap: 8px;">';
        echo '<button style="padding: 8px 12px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Previous</button>';
        echo '<button style="padding: 8px 12px; border: 1px solid #6366f1; background: #6366f1; color: white; border-radius: 6px; cursor: pointer;">1</button>';
        echo '<button style="padding: 8px 12px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Next</button>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div style="text-align: center; padding: 64px; color: #6b7280;">';
        echo '<i class="fas fa-sitemap" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>';
        echo '<h3 style="margin: 0 0 8px 0;">No subnets found</h3>';
        echo '<p style="margin: 0;">Create your first subnet to get started with IP address management.</p>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Add New Subnet Modal
    dcim_render_add_subnet_modal($modulelink);

    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    dcim_render_subnet_javascript($modulelink);
}

/**
 * Render the Add New Subnet Modal
 */
function dcim_render_add_subnet_modal($modulelink) {
    echo '<div id="addSubnetModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">';
    echo '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">';

    // Modal header
    echo '<div style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">';
    echo '<div style="display: flex; justify-content: space-between; align-items: center;">';
    echo '<h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add New Subnet</h2>';
    echo '<button onclick="closeAddSubnetModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>';
    echo '</div>';
    echo '</div>';

    // Modal form
    echo '<form method="post" style="padding: 0 24px 24px 24px;">';
    echo '<input type="hidden" name="action" value="create_subnet">';

    // IP Version
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">IP Version *</label>';
    echo '<select name="ip_version" required style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option value="IPv4">IPv4</option>';
    echo '<option value="IPv6">IPv6</option>';
    echo '</select>';
    echo '</div>';

    // Subnet (CIDR notation)
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Subnet (CIDR notation) *</label>';
    echo '<input type="text" name="subnet" placeholder="e.g., ***********/24" required style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '</div>';

    // Country
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Country</label>';
    echo '<select name="country" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option value="">-- Select Country --</option>';
    echo '<option value="Romania">Romania</option>';
    echo '<option value="United States">United States</option>';
    echo '<option value="Germany">Germany</option>';
    echo '<option value="United Kingdom">United Kingdom</option>';
    echo '<option value="France">France</option>';
    echo '</select>';
    echo '</div>';

    // City
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">City *</label>';
    echo '<select name="city" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option value="">-- Select City --</option>';
    echo '<option value="Bucharest">Bucharest</option>';
    echo '<option value="New York">New York</option>';
    echo '<option value="Berlin">Berlin</option>';
    echo '<option value="London">London</option>';
    echo '<option value="Paris">Paris</option>';
    echo '</select>';
    echo '</div>';

    // Public Subnet
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Public Subnet</label>';
    echo '<div style="display: flex; gap: 16px;">';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="is_public" value="1" style="margin: 0;">';
    echo '<span>Yes</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="is_public" value="0" checked style="margin: 0;">';
    echo '<span>No</span>';
    echo '</label>';
    echo '</div>';
    echo '</div>';

    // Subnet Type
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Subnet Type</label>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Root" checked style="margin: 0;">';
    echo '<span>Root</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Customer" style="margin: 0;">';
    echo '<span>Customer</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Management" style="margin: 0;">';
    echo '<span>Management</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Transit" style="margin: 0;">';
    echo '<span>Transit</span>';
    echo '</label>';
    echo '</div>';
    echo '</div>';

    // Note
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Note</label>';
    echo '<textarea name="note" placeholder="Optional note for this subnet..." style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; min-height: 80px; resize: vertical;"></textarea>';
    echo '</div>';

    // Modal footer
    echo '<div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #e5e7eb;">';
    echo '<button type="button" onclick="closeAddSubnetModal()" style="padding: 10px 20px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="padding: 10px 20px; border: none; background: #6366f1; color: white; border-radius: 6px; cursor: pointer;">Add Subnet</button>';
    echo '</div>';

    echo '</form>';
    echo '</div>';
    echo '</div>';
}

/**
 * Render JavaScript functions for subnet management
 */
function dcim_render_subnet_javascript($modulelink) {
    echo '<script>
    function showAddSubnetModal() {
        document.getElementById("addSubnetModal").style.display = "block";
    }

    function closeAddSubnetModal() {
        document.getElementById("addSubnetModal").style.display = "none";
    }

    function viewSubnetDetails(subnetId) {
        // Fetch subnet details via AJAX and show modal
        fetch("' . $modulelink . '&action=ipam&subaction=subnet_details&subnet_id=" + subnetId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSubnetDetailsModal(data.subnet);
                } else {
                    alert("Error loading subnet details: " + data.error);
                }
            })
            .catch(error => {
                console.error("Error:", error);
                alert("Error loading subnet details");
            });
    }

    function showSubnetDetailsModal(subnet) {
        var modal = document.createElement("div");
        modal.id = "subnetDetailsModal";
        modal.style.cssText = "display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;";

        var modalContent = `
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; width: 90%; max-width: 800px; max-height: 90vh; overflow-y: auto;">
                <div style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Subnet Details</h2>
                        <button onclick="closeSubnetDetailsModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>
                    </div>
                </div>

                <div style="padding: 0 24px 24px 24px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                        <h1 style="margin: 0; font-size: 24px; font-weight: 600;">${subnet.subnet}</h1>
                        <span style="background: #dcfce7; color: #166534; padding: 6px 12px; border-radius: 6px; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-check-circle" style="margin-right: 4px;"></i>${subnet.status}
                        </span>
                    </div>

                    <div style="margin-bottom: 32px;">
                        <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">Subnet Information</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <div>
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Location</div>
                                <div style="font-weight: 500;">${subnet.city || "Unassigned"}</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Gateway</div>
                                <div style="font-weight: 500;">${subnet.gateway || subnet.network + ".1"}</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Category</div>
                                <div style="font-weight: 500;">${subnet.subnet_type}</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Assigned To</div>
                                <div style="font-weight: 500;">Unassigned</div>
                            </div>
                            <div>
                                <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">Last Updated</div>
                                <div style="font-weight: 500;">${new Date(subnet.updated_at).toLocaleDateString()}</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 32px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3 style="margin: 0; font-size: 16px; font-weight: 600;">Note</h3>
                            <button style="background: none; border: none; color: #6b7280; cursor: pointer;"><i class="fas fa-edit"></i></button>
                        </div>
                        <div style="color: #6b7280;">${subnet.note || "No note added yet."}</div>
                    </div>

                    <div style="margin-bottom: 32px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <h3 style="margin: 0; font-size: 16px; font-weight: 600;"><i class="fas fa-sitemap" style="margin-right: 8px;"></i>Subnet Hierarchy</h3>
                        </div>
                        <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;">
                            <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">Root Subnets:</div>
                            <div style="margin-left: 16px; color: #6b7280;">
                                <div><i class="fas fa-sitemap" style="margin-right: 8px;"></i>${subnet.subnet} <span style="color: #10b981;">Available</span></div>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                        <button style="padding: 10px 20px; border: none; background: #f3f4f6; color: #374151; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic" style="margin-right: 6px;"></i>Generate IPs</button>
                        <button style="padding: 10px 20px; border: none; background: #6b7280; color: white; border-radius: 6px; cursor: pointer;"><i class="fas fa-bookmark" style="margin-right: 6px;"></i>Reserve Subnet</button>
                        <button onclick="deleteSubnet(${subnet.id}); closeSubnetDetailsModal();" style="padding: 10px 20px; border: none; background: #dc2626; color: white; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash" style="margin-right: 6px;"></i>Delete Subnet</button>
                    </div>
                </div>
            </div>
        `;

        modal.innerHTML = modalContent;
        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener("click", function(e) {
            if (e.target === modal) {
                closeSubnetDetailsModal();
            }
        });
    }

    function closeSubnetDetailsModal() {
        var modal = document.getElementById("subnetDetailsModal");
        if (modal) {
            modal.remove();
        }
    }

    function deleteSubnet(subnetId) {
        if (confirm("Are you sure you want to delete this subnet? This will also delete all associated IP addresses.")) {
            var form = document.createElement("form");
            form.method = "post";
            form.style.display = "none";

            var actionInput = document.createElement("input");
            actionInput.type = "hidden";
            actionInput.name = "action";
            actionInput.value = "delete_subnet";
            form.appendChild(actionInput);

            var subnetIdInput = document.createElement("input");
            subnetIdInput.type = "hidden";
            subnetIdInput.name = "subnet_id";
            subnetIdInput.value = subnetId;
            form.appendChild(subnetIdInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Close modal when clicking outside
    document.getElementById("addSubnetModal").addEventListener("click", function(e) {
        if (e.target === this) {
            closeAddSubnetModal();
        }
    });
    </script>';
}

/**
 * Placeholder functions for other IPAM features
 */
function dcim_manage_ips($modulelink) {
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    dcim_generate_sidebar($modulelink);
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">IP Address Management</div>';
    echo '</div>';
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    echo '<h2>IP Address Management</h2>';
    echo '<p>This feature is coming soon.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    dcim_generate_sidebar_javascript($modulelink);
}

function dcim_ip_allocation($modulelink) {
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    dcim_generate_sidebar($modulelink);
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">IP Allocation</div>';
    echo '</div>';
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    echo '<h2>IP Allocation</h2>';
    echo '<p>This feature is coming soon.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Force create IPAM tables if they don't exist
 */
function dcim_force_create_ipam_tables() {
    try {
        // Check and create IPAM subnets table
        if (!Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::schema()->create('dcim_subnets', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('subnet'); // Full CIDR notation, e.g., ***********/24
                $table->string('network'); // Network address, e.g., ***********
                $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
                $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
                $table->string('country')->nullable();
                $table->string('city')->nullable();
                $table->boolean('is_public')->default(false);
                $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
                $table->string('gateway')->nullable();
                $table->string('dns_primary')->nullable();
                $table->string('dns_secondary')->nullable();
                $table->string('vlan_id')->nullable();
                $table->text('note')->nullable();
                $table->enum('status', ['Available', 'Parent of Allocated Subnet', 'Reserved', 'Deprecated'])->default('Available');
                $table->timestamps();

                // Add foreign key to locations if it exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
                }

                // Add unique constraint on subnet CIDR
                $table->unique(['subnet']);
            });

            echo '<div class="alert alert-success">Created dcim_subnets table successfully.</div>';
        }

        // Check and create IPAM IP addresses table
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::schema()->create('dcim_ip_addresses', function ($table) {
                $table->increments('id');
                $table->integer('subnet_id')->unsigned();
                $table->string('ip_address');
                $table->string('hostname')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
                $table->timestamps();

                // Add foreign key to subnets
                $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');

                // Ensure each IP is unique within a subnet
                $table->unique(['subnet_id', 'ip_address']);
            });

            echo '<div class="alert alert-success">Created dcim_ip_addresses table successfully.</div>';
        }

        // Check and create IPAM IP assignments table (links IPs to devices)
        if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
            Capsule::schema()->create('dcim_ip_assignments', function ($table) {
                $table->increments('id');
                $table->integer('ip_address_id')->unsigned();
                $table->enum('device_type', ['server', 'switch', 'chassis']);
                $table->integer('device_id')->unsigned();
                $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
                $table->timestamps();

                // Add foreign key to IP addresses
                $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');

                // Ensure one IP can't be assigned multiple times to the same interface
                $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
            });

            echo '<div class="alert alert-success">Created dcim_ip_assignments table successfully.</div>';
        }

        return true;
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error creating IPAM tables: ' . htmlspecialchars($e->getMessage()) . '</div>';
        error_log("DCIM: Error creating IPAM tables - " . $e->getMessage());
        return false;
    }
}
